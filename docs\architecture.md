# Card Canvas Application - Technical Architecture

## **Introduction (项目介绍)**

### **Project Overview (项目概述)**
本文档定义了卡片画布应用的完整技术架构。这是一个本地优先的Web应用，允许用户在画布上创建、编辑和连接卡片，支持Markdown内容编辑，并能将连接的卡片作为上下文发送给AI代理进行对话。

### **Architecture Type (架构类型)**
- **Greenfield Project** - 全新项目，无遗留系统约束
- **Local-First Architecture** - 本地优先，数据存储在浏览器中
- **Single-Page Application** - React-based SPA
- **No Backend Server** - 纯前端应用，使用SQLite WASM作为数据库

### **Change Log (变更日志)**
| Version | Date | Changes | Author |
|---------|------|---------|---------|
| 1.0.0 | 2024-01-31 | Initial architecture document | Winston (Architect) |

---

## **High Level Architecture (高层架构)**

### **Technical Summary (技术摘要)**
本地优先的React应用，使用SQLite WASM进行数据持久化，Konva.js处理2D画布渲染，通过OpenRouter API集成多种AI模型。应用采用组件化架构，支持离线使用，数据完全由用户控制。

### **Platform Choice (平台选择)**
- **Primary Platform:** Web (浏览器)
- **Deployment Target:** 本地开发环境 (Vite Dev Server)
- **Browser Support:** 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- **Mobile Support:** 响应式设计，支持平板和手机访问

### **Repository Structure (仓库结构)**
```
refly_sqlite/                 # 项目根目录
├── apps/                     # 应用程序
│   └── web/                  # 主Web应用
├── packages/                 # 共享包
│   ├── shared/               # 共享类型和工具
│   └── ui/                   # UI组件库
├── docs/                     # 项目文档
└── .bmad-core/              # BMad方法论配置
```

**Repository Type:** Monorepo with npm workspaces

### **Architecture Diagram (架构图)**
```
┌─────────────────────────────────────────────────────────────┐
│                    Browser Environment                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   React App     │  │  Konva Canvas   │  │   AI Chat UI    │ │
│  │   (UI Layer)    │  │  (Rendering)    │  │   (Interaction) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  State Manager  │  │  Service Layer  │  │  Cache Manager  │ │
│  │   (Zustand)     │  │  (Business)     │  │   (LRU Cache)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ SQLite Service  │  │ Markdown Proc.  │  │ Encryption Svc  │ │
│  │  (Data Layer)   │  │  (Content)      │  │   (Security)    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ SQLite WASM     │  │   IndexedDB     │                    │
│  │  (Database)     │  │   (Storage)     │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  OpenRouter API │
                    │   (External)    │
                    └─────────────────┘
```

### **Architectural Patterns (架构模式)**
- **Repository Pattern** - 数据访问抽象
- **Service Layer Pattern** - 业务逻辑封装
- **Component-Based Architecture** - React组件化
- **Event-Driven Architecture** - 组件间通信
- **Local-First Pattern** - 本地数据优先
- **Progressive Web App (PWA)** - 离线支持和缓存

---

## **Tech Stack (技术栈)**

### **Technology Selection Table (技术选择表)**

| Category | Technology | Version | Rationale |
|----------|------------|---------|-----------|
| **Frontend Framework** | React | 18.2+ | 成熟的组件化框架，丰富的生态系统，优秀的开发体验 |
| **Language** | TypeScript | 5.3+ | 类型安全，更好的开发体验，减少运行时错误 |
| **Build Tool** | Vite | 5.0+ | 快速的开发服务器，优化的生产构建，现代化工具链 |
| **UI Framework** | TailwindCSS | 3.4+ | 实用优先的CSS框架，快速开发，一致的设计系统 |
| **Component Library** | Headless UI | 1.7+ | 无样式的可访问组件，与TailwindCSS完美集成 |
| **Canvas Rendering** | Konva.js | 9.2+ | 高性能2D画布库，支持复杂图形操作和事件处理 |
| **State Management** | Zustand | 4.4+ | 轻量级状态管理，简单API，TypeScript友好 |
| **Database** | SQLite WASM | 3.44+ | 浏览器中的完整SQL数据库，支持复杂查询和事务 |
| **HTTP Client** | Alova.js | 2.17+ | 现代HTTP客户端，支持重试、缓存和错误处理 |
| **Markdown Processing** | marked.js | 11.0+ | 快速的Markdown解析器，支持扩展和自定义渲染 |
| **Code Highlighting** | highlight.js | 11.9+ | 语法高亮库，支持多种编程语言 |
| **Icons** | Lucide React | 0.300+ | 现代化图标库，SVG格式，可定制性强 |
| **Date Handling** | date-fns | 3.0+ | 现代化日期处理库，模块化设计，TypeScript支持 |
| **Validation** | Zod | 3.22+ | TypeScript优先的模式验证库，类型安全 |
| **Testing Framework** | Vitest | 1.1+ | 快速的单元测试框架，与Vite集成良好 |
| **E2E Testing** | Playwright | 1.40+ | 现代化端到端测试框架，支持多浏览器 |
| **Testing Library** | React Testing Library | 14.1+ | React组件测试的最佳实践库 |
| **Linting** | ESLint | 8.56+ | JavaScript/TypeScript代码质量检查 |
| **Formatting** | Prettier | 3.1+ | 代码格式化工具，保持代码风格一致 |
| **Package Manager** | npm | 10.2+ | Node.js包管理器，支持workspaces |

### **Rationale for Key Technology Choices (关键技术选择理由)**

#### **React 18+ with TypeScript**
- **成熟生态系统**: 丰富的组件库和工具链
- **并发特性**: React 18的并发渲染提升性能
- **类型安全**: TypeScript减少运行时错误，提升开发效率
- **团队熟悉度**: 广泛使用的技术栈，易于维护

#### **Konva.js for Canvas Rendering**
- **高性能**: 基于Canvas 2D API，支持硬件加速
- **事件处理**: 完整的鼠标/触摸事件支持
- **图形操作**: 支持拖拽、缩放、旋转等复杂操作
- **React集成**: react-konva提供良好的React集成

#### **SQLite WASM**
- **完整SQL支持**: 支持复杂查询、事务、触发器
- **本地优先**: 数据完全存储在浏览器中
- **性能优异**: 比IndexedDB更好的查询性能
- **数据完整性**: ACID事务保证数据一致性

#### **Zustand for State Management**
- **轻量级**: 仅2KB，比Redux轻量得多
- **简单API**: 学习曲线平缓，代码简洁
- **TypeScript友好**: 原生TypeScript支持
- **无样板代码**: 不需要复杂的action/reducer模式

---

## **Data Models (数据模型)**

### **Core Data Models (核心数据模型)**

#### **Canvas Model (画布模型)**
```typescript
interface Canvas {
  id: string;                    // UUID
  name: string;                  // 画布名称
  description?: string;          // 画布描述
  theme: CanvasTheme;           // 主题配置
  viewport: {                   // 视口状态
    x: number;
    y: number;
    zoom: number;
  };
  settings: {                   // 画布设置
    gridEnabled: boolean;
    snapToGrid: boolean;
    gridSize: number;
    backgroundColor: string;
  };
  created_at: Date;
  updated_at: Date;
}

enum CanvasTheme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}
```

#### **Card Model (卡片模型)**
```typescript
interface Card {
  id: string;                    // UUID
  canvas_id: string;             // 所属画布ID
  title: string;                 // 卡片标题（从内容提取）
  content: string;               // Markdown内容
  position_x: number;            // X坐标
  position_y: number;            // Y坐标
  width: number;                 // 宽度
  height: number;                // 高度
  z_index: number;               // 层级
  style: {                      // 样式配置
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    fontSize: number;
  };
  tags: string[];               // 标签数组
  created_at: Date;
  updated_at: Date;
}
```

#### **Connection Model (连接模型)**
```typescript
interface Connection {
  id: string;                    // UUID
  canvas_id: string;             // 所属画布ID
  source_card_id: string;        // 源卡片ID
  target_card_id: string;        // 目标卡片ID
  source_anchor: AnchorPoint;    // 源锚点
  target_anchor: AnchorPoint;    // 目标锚点
  type: ConnectionType;          // 连接类型
  style: {                      // 连接样式
    color: string;
    width: number;
    dashArray?: number[];
  };
  label?: string;               // 连接标签
  created_at: Date;
  updated_at: Date;
}

enum AnchorPoint {
  TOP = 'top',
  RIGHT = 'right',
  BOTTOM = 'bottom',
  LEFT = 'left'
}

enum ConnectionType {
  ARROW = 'arrow',
  LINE = 'line',
  CURVE = 'curve'
}
```

#### **AI Conversation Model (AI对话模型)**
```typescript
interface AIConversation {
  id: string;                    // UUID
  canvas_id: string;             // 关联画布ID
  title: string;                 // 对话标题
  model: string;                 // AI模型名称
  messages: AIMessage[];         // 消息列表
  context_cards: string[];       // 上下文卡片ID列表
  created_at: Date;
  updated_at: Date;
}

interface AIMessage {
  id: string;                    // 消息ID
  role: 'user' | 'assistant';    // 消息角色
  content: string;               // 消息内容
  timestamp: Date;               // 时间戳
  metadata?: {                  // 元数据
    model?: string;
    tokens?: number;
    cost?: number;
  };
}
```

### **Data Relationships (数据关系)**
```
Canvas (1) ──── (N) Card
Canvas (1) ──── (N) Connection
Canvas (1) ──── (N) AIConversation
Card (1) ──── (N) Connection (as source)
Card (1) ──── (N) Connection (as target)
AIConversation (1) ──── (N) AIMessage
```

---

## **API Specification (API规范)**

### **External API Integration (外部API集成)**

#### **OpenRouter API Integration**
```typescript
// OpenRouter API配置
interface OpenRouterConfig {
  baseURL: 'https://openrouter.ai/api/v1';
  apiKey: string;                // 用户提供的API密钥
  defaultModel: string;          // 默认模型
  timeout: number;               // 请求超时时间
  retryAttempts: number;         // 重试次数
}

// 聊天完成请求
interface ChatCompletionRequest {
  model: string;                 // 模型名称
  messages: {
    role: 'user' | 'assistant' | 'system';
    content: string;
  }[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

// 聊天完成响应
interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: 'assistant';
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}
```

#### **API Error Handling**
```typescript
interface APIError {
  error: {
    message: string;
    type: string;
    code: string;
  };
}

// 错误处理示例
try {
  const response = await openRouterClient.chat.completions.create(request);
  return response;
} catch (error) {
  if (error.status === 401) {
    throw new Error('API密钥无效');
  } else if (error.status === 429) {
    throw new Error('请求频率过高，请稍后重试');
  } else if (error.status >= 500) {
    throw new Error('AI服务暂时不可用');
  }
  throw error;
}
```

### **Internal Service Layer APIs (内部服务层API)**

#### **Canvas Service API**
```typescript
interface CanvasService {
  // 画布管理
  createCanvas(data: CreateCanvasRequest): Promise<Canvas>;
  getCanvas(id: string): Promise<Canvas>;
  updateCanvas(id: string, updates: Partial<Canvas>): Promise<Canvas>;
  deleteCanvas(id: string): Promise<void>;
  listCanvases(): Promise<Canvas[]>;

  // 画布操作
  duplicateCanvas(id: string): Promise<Canvas>;
  exportCanvas(id: string, format: 'json' | 'markdown'): Promise<string>;
  importCanvas(data: string, format: 'json' | 'markdown'): Promise<Canvas>;
}
```

#### **Card Service API**
```typescript
interface CardService {
  // 卡片CRUD
  createCard(data: CreateCardRequest): Promise<Card>;
  getCard(id: string): Promise<Card>;
  updateCard(id: string, updates: Partial<Card>): Promise<Card>;
  deleteCard(id: string): Promise<void>;

  // 卡片查询
  getCardsByCanvas(canvasId: string): Promise<Card[]>;
  searchCards(query: string, canvasId?: string): Promise<Card[]>;
  getCardsByTags(tags: string[], canvasId?: string): Promise<Card[]>;

  // 卡片操作
  duplicateCard(id: string): Promise<Card>;
  moveCard(id: string, position: { x: number; y: number }): Promise<void>;
  resizeCard(id: string, size: { width: number; height: number }): Promise<void>;
}
```

### **Event System Interfaces (事件系统接口)**
```typescript
interface EventBus {
  // 事件发布
  emit<T>(event: string, data: T): void;

  // 事件订阅
  on<T>(event: string, handler: (data: T) => void): () => void;

  // 一次性事件订阅
  once<T>(event: string, handler: (data: T) => void): () => void;

  // 取消订阅
  off(event: string, handler?: Function): void;
}

// 系统事件定义
type SystemEvents = {
  'card:created': Card;
  'card:updated': { id: string; updates: Partial<Card> };
  'card:deleted': { id: string };
  'connection:created': Connection;
  'connection:deleted': { id: string };
  'canvas:switched': { fromId: string; toId: string };
  'ai:message:sent': { conversationId: string; message: AIMessage };
  'ai:message:received': { conversationId: string; message: AIMessage };
};

---

## **Components (组件架构)**

### **Major Components Overview (主要组件概述)**

#### **1. CanvasEngine (画布引擎)**
**Responsibility:** 管理2D画布渲染、用户交互和视口控制

```typescript
interface CanvasEngine {
  // 画布管理
  initializeCanvas(container: HTMLElement): void;
  destroyCanvas(): void;
  resizeCanvas(width: number, height: number): void;

  // 视口控制
  setViewport(x: number, y: number, zoom: number): void;
  getViewport(): { x: number; y: number; zoom: number };
  zoomToFit(cards: Card[]): void;

  // 渲染管理
  renderCards(cards: Card[]): void;
  renderConnections(connections: Connection[]): void;
  updateCard(card: Card): void;
  removeCard(cardId: string): void;

  // 交互处理
  enableCardDragging(enabled: boolean): void;
  enableMultiSelection(enabled: boolean): void;
  getSelectedCards(): string[];

  // 事件处理
  onCardClick(handler: (cardId: string) => void): void;
  onCardDoubleClick(handler: (cardId: string) => void): void;
  onCardDrag(handler: (cardId: string, position: { x: number; y: number }) => void): void;
  onConnectionCreate(handler: (sourceId: string, targetId: string) => void): void;
}
```

#### **2. DatabaseManager (数据库管理器)**
**Responsibility:** SQLite WASM数据库操作和数据持久化

```typescript
interface DatabaseManager {
  // 数据库生命周期
  initialize(): Promise<void>;
  close(): Promise<void>;
  backup(): Promise<Uint8Array>;
  restore(data: Uint8Array): Promise<void>;

  // 事务管理
  transaction<T>(callback: (tx: Transaction) => Promise<T>): Promise<T>;

  // 查询执行
  executeQuery<T>(sql: string, params?: any[]): Promise<T[]>;
  executeNonQuery(sql: string, params?: any[]): Promise<void>;

  // 数据库维护
  vacuum(): Promise<void>;
  analyze(): Promise<void>;
  getStats(): Promise<DatabaseStats>;
}

interface DatabaseStats {
  size: number;
  pageCount: number;
  freePages: number;
  tableStats: Record<string, { rowCount: number; size: number }>;
}
```

#### **3. AIIntegrationService (AI集成服务)**
**Responsibility:** 管理AI模型交互、上下文格式化和对话历史

```typescript
interface AIIntegrationService {
  // 模型管理
  getAvailableModels(): Promise<AIModel[]>;
  setDefaultModel(modelId: string): void;

  // 对话管理
  createConversation(canvasId: string, contextCards: string[]): Promise<AIConversation>;
  sendMessage(conversationId: string, message: string): Promise<AIMessage>;
  getConversationHistory(conversationId: string): Promise<AIMessage[]>;

  // 上下文处理
  formatCardsAsContext(cards: Card[]): string;
  estimateTokenCount(text: string): number;
  optimizeContext(cards: Card[], maxTokens: number): Card[];

  // 流式响应
  sendMessageStream(
    conversationId: string,
    message: string,
    onChunk: (chunk: string) => void
  ): Promise<AIMessage>;
}

interface AIModel {
  id: string;
  name: string;
  provider: string;
  contextLength: number;
  costPer1kTokens: number;
  capabilities: string[];
}
```

#### **4. StateManager (状态管理器)**
**Responsibility:** 应用状态管理、状态同步和持久化

```typescript
interface AppState {
  // 当前状态
  currentCanvas: Canvas | null;
  cards: Record<string, Card>;
  connections: Record<string, Connection>;
  selectedCards: string[];

  // UI状态
  ui: {
    sidebarOpen: boolean;
    chatPanelOpen: boolean;
    settingsOpen: boolean;
    loading: boolean;
    error: string | null;
  };

  // 用户设置
  settings: {
    theme: 'light' | 'dark' | 'auto';
    autoSave: boolean;
    gridEnabled: boolean;
    snapToGrid: boolean;
    defaultCardSize: { width: number; height: number };
    aiModel: string;
    apiKey: string;
  };

  // AI状态
  ai: {
    conversations: Record<string, AIConversation>;
    activeConversation: string | null;
    isGenerating: boolean;
  };
}

interface StateManager {
  // 状态访问
  getState(): AppState;
  subscribe(listener: (state: AppState) => void): () => void;

  // 状态更新
  updateCanvas(canvas: Canvas): void;
  updateCard(card: Card): void;
  updateConnection(connection: Connection): void;
  setSelectedCards(cardIds: string[]): void;

  // 批量操作
  batchUpdate(updates: StateUpdate[]): void;

  // 持久化
  saveState(): Promise<void>;
  loadState(): Promise<void>;
}
```

#### **5. MarkdownProcessor (Markdown处理器)**
**Responsibility:** Markdown内容解析、渲染和编辑支持

```typescript
interface MarkdownProcessor {
  // 解析和渲染
  parseMarkdown(content: string): ParsedMarkdown;
  renderToHTML(content: string): string;
  renderToPlainText(content: string): string;

  // 内容提取
  extractTitle(content: string): string;
  extractTags(content: string): string[];
  extractLinks(content: string): string[];

  // 内容验证
  validateMarkdown(content: string): ValidationResult;
  sanitizeContent(content: string): string;

  // 编辑支持
  insertAtCursor(content: string, insertion: string, cursorPos: number): string;
  formatSelection(content: string, start: number, end: number, format: MarkdownFormat): string;
}

interface ParsedMarkdown {
  title: string;
  content: string;
  headings: { level: number; text: string; id: string }[];
  links: { text: string; url: string }[];
  tags: string[];
  wordCount: number;
}

enum MarkdownFormat {
  BOLD = 'bold',
  ITALIC = 'italic',
  CODE = 'code',
  LINK = 'link',
  HEADING = 'heading'
}

#### **6. CacheManager (缓存管理器)**
**Responsibility:** 内存缓存、性能优化和数据预加载

```typescript
interface CacheManager {
  // 缓存操作
  set<T>(key: string, value: T, ttl?: number): void;
  get<T>(key: string): T | null;
  has(key: string): boolean;
  delete(key: string): void;
  clear(): void;

  // 缓存策略
  setMaxSize(size: number): void;
  getStats(): CacheStats;

  // 预加载
  preloadCanvas(canvasId: string): Promise<void>;
  preloadCards(cardIds: string[]): Promise<void>;

  // 缓存失效
  invalidate(pattern: string): void;
  invalidateCanvas(canvasId: string): void;
}

interface CacheStats {
  size: number;
  maxSize: number;
  hitRate: number;
  missRate: number;
  evictions: number;
}
```

#### **7. ExportManager (导出管理器)**
**Responsibility:** 数据导出、格式转换和备份功能

```typescript
interface ExportManager {
  // 导出功能
  exportCanvas(canvasId: string, format: ExportFormat): Promise<string>;
  exportCards(cardIds: string[], format: ExportFormat): Promise<string>;
  exportConversation(conversationId: string, format: ExportFormat): Promise<string>;

  // 导入功能
  importCanvas(data: string, format: ExportFormat): Promise<Canvas>;
  importCards(data: string, canvasId: string, format: ExportFormat): Promise<Card[]>;

  // 备份功能
  createFullBackup(): Promise<Uint8Array>;
  restoreFromBackup(data: Uint8Array): Promise<void>;

  // 格式转换
  convertMarkdownToHTML(content: string): string;
  convertCardsToMarkdown(cards: Card[]): string;
  convertConversationToMarkdown(conversation: AIConversation): string;
}

enum ExportFormat {
  JSON = 'json',
  MARKDOWN = 'markdown',
  HTML = 'html',
  CSV = 'csv',
  PDF = 'pdf'
}
```

---

## **External APIs (外部API集成)**

### **OpenRouter API Integration (OpenRouter API集成)**

#### **API Client Implementation (API客户端实现)**
```typescript
class OpenRouterClient {
  private config: OpenRouterConfig;
  private httpClient: HttpClient;

  constructor(config: OpenRouterConfig) {
    this.config = config;
    this.httpClient = new HttpClient({
      baseURL: config.baseURL,
      timeout: config.timeout,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Card Canvas App'
      }
    });
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      const response = await this.httpClient.post('/chat/completions', request);
      return response.data;
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  async createChatCompletionStream(
    request: ChatCompletionRequest,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const streamRequest = { ...request, stream: true };

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: this.httpClient.getHeaders(),
        body: JSON.stringify(streamRequest)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) onChunk(content);
            } catch (e) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  async getModels(): Promise<AIModel[]> {
    try {
      const response = await this.httpClient.get('/models');
      return response.data.data.map(this.transformModel);
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  private handleAPIError(error: any): Error {
    if (error.response?.status === 401) {
      return new Error('API密钥无效或已过期');
    } else if (error.response?.status === 429) {
      return new Error('请求频率过高，请稍后重试');
    } else if (error.response?.status >= 500) {
      return new Error('AI服务暂时不可用');
    } else if (error.code === 'NETWORK_ERROR') {
      return new Error('网络连接失败');
    }
    return new Error(`API请求失败: ${error.message}`);
  }

  private transformModel(apiModel: any): AIModel {
    return {
      id: apiModel.id,
      name: apiModel.name || apiModel.id,
      provider: apiModel.owned_by || 'unknown',
      contextLength: apiModel.context_length || 4096,
      costPer1kTokens: apiModel.pricing?.prompt || 0,
      capabilities: apiModel.capabilities || []
    };
  }
}
```

#### **Context Formatting (上下文格式化)**
```typescript
class ContextFormatter {
  static formatCardsAsContext(cards: Card[]): string {
    const sections = cards.map(card => {
      const title = card.title || '无标题卡片';
      const content = card.content.trim();
      const tags = card.tags.length > 0 ? `\n标签: ${card.tags.join(', ')}` : '';

      return `## ${title}\n\n${content}${tags}`;
    });

    return [
      '# 卡片上下文',
      '',
      '以下是用户选择的相关卡片内容，请基于这些信息回答问题：',
      '',
      ...sections
    ].join('\n');
  }

  static estimateTokenCount(text: string): number {
    // 简单的token估算：中文字符 * 1.5 + 英文单词数
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return Math.ceil(chineseChars * 1.5 + englishWords);
  }

  static optimizeContext(cards: Card[], maxTokens: number): Card[] {
    let totalTokens = 0;
    const optimizedCards: Card[] = [];

    // 按创建时间排序，优先包含最新的卡片
    const sortedCards = [...cards].sort((a, b) =>
      b.created_at.getTime() - a.created_at.getTime()
    );

    for (const card of sortedCards) {
      const cardTokens = this.estimateTokenCount(
        this.formatCardsAsContext([card])
      );

      if (totalTokens + cardTokens <= maxTokens) {
        optimizedCards.push(card);
        totalTokens += cardTokens;
      } else {
        break;
      }
    }

    return optimizedCards;
  }
}

---

## **Document Status (文档状态)**

### **Completed Sections (已完成章节)**
✅ **Introduction** - 项目介绍和变更日志
✅ **High Level Architecture** - 高层架构概述
✅ **Tech Stack** - 技术栈选择和理由
✅ **Data Models** - 数据模型定义
✅ **API Specification** - API规范
✅ **Components** - 组件架构
✅ **External APIs** - 外部API集成

### **Remaining Sections (待添加章节)**
📋 **Core Workflows** - 核心工作流程
📋 **Database Schema** - 数据库架构
📋 **Frontend Architecture** - 前端架构
📋 **Backend Architecture** - 后端架构
📋 **Unified Project Structure** - 统一项目结构
📋 **Development Workflow** - 开发工作流
📋 **Deployment Architecture** - 部署架构
📋 **Security and Performance** - 安全和性能
📋 **Testing Strategy** - 测试策略
📋 **Coding Standards** - 编码标准
📋 **Error Handling Strategy** - 错误处理策略
📋 **Monitoring and Observability** - 监控与可观测性
📋 **Checklist Results Report** - 检查清单结果报告

### **Document Completion Status**
**Current Progress:** 35% (7/20 sections completed)
**Estimated Total Length:** ~3000+ lines when complete
**File Size Limit:** 300 lines per save operation

### **Next Steps**
由于文档长度限制，完整的架构文档需要通过多次编辑操作来完成。当前已包含核心的架构设计部分，包括：

1. **技术栈定义** - 20+技术选择和版本
2. **数据模型** - 4个核心实体和关系
3. **API规范** - 外部和内部API接口
4. **组件架构** - 7个主要组件设计
5. **外部集成** - OpenRouter API实现

剩余章节将包含详细的实施指导、数据库设计、安全策略、测试方案等内容。

---

*注：这是一个分段保存的架构文档。完整文档包含所有20个主要章节，总计约3000+行详细的技术规范和实施指导。*
```
```